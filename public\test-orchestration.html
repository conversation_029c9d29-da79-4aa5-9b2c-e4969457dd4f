<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Content Management Orchestration</title>
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/css/footer-module.css">
    <link rel="stylesheet" href="/css/button-module.css">
    <link rel="stylesheet" href="/css/typography-module.css">
    <link rel="stylesheet" href="/editor.css">
    <script type="module" src="/js/visual-editor-v2.js?v=20250101-CACHE-BUST&t=1735747300" defer></script>
    <style>
        .test-container {
            max-width: 1000px;
            margin: 40px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .orchestration-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background: white;
        }
        .log-entry.step-1 { border-left-color: #28a745; }
        .log-entry.step-2 { border-left-color: #ffc107; }
        .log-entry.step-3 { border-left-color: #17a2b8; }
        .log-entry.step-4 { border-left-color: #dc3545; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎭 Content Management Orchestration Test</h1>
        
        <div class="test-section">
            <h2>📋 Test Overview</h2>
            <p>This page tests the orchestrated content management flow:</p>
            <ol>
                <li><strong>📄 Original HTML loaded</strong> - Base foundation</li>
                <li><strong>🏗️ Dynamic sections placed</strong> - Structure changes</li>
                <li><strong>✏️ Content overrides applied</strong> - Content modifications</li>
                <li><strong>🔄 Section reordering enacted</strong> - Visual arrangement</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🎯 Orchestration Log</h2>
            <div id="orchestration-log" class="orchestration-log">
                <div class="log-entry">Waiting for orchestration to begin...</div>
            </div>
        </div>

        <main>
            <section data-ve-section-id="test-section-1">
                <h2 data-ve-block-id="test-heading-1">Test Section 1</h2>
                <p data-ve-block-id="test-content-1">This is test content that can be edited.</p>
            </section>

            <section data-ve-section-id="test-section-2">
                <h2 data-ve-block-id="test-heading-2">Test Section 2</h2>
                <p data-ve-block-id="test-content-2">This is another test section for reordering.</p>
            </section>

            <section data-ve-section-id="dynamicSections">
                <!-- Dynamic sections will be inserted here -->
            </section>
        </main>
    </div>

    <script>
        // Capture orchestration logs
        const logContainer = document.getElementById('orchestration-log');
        const originalConsoleLog = console.log;
        
        function addLogEntry(message, step = '') {
            const entry = document.createElement('div');
            entry.className = `log-entry ${step}`;
            entry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Override console.log to capture orchestration messages
        console.log = function(...args) {
            const message = args.join(' ');
            
            // Detect orchestration messages
            if (message.includes('[ORCHESTRATOR]')) {
                let step = '';
                if (message.includes('Step 1')) step = 'step-1';
                else if (message.includes('Step 2')) step = 'step-2';
                else if (message.includes('Step 3')) step = 'step-3';
                else if (message.includes('Step 4')) step = 'step-4';
                
                addLogEntry(message, step);
            }
            
            // Call original console.log
            originalConsoleLog.apply(console, args);
        };

        // Clear initial message when orchestration starts
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (logContainer.children.length === 1) {
                    logContainer.innerHTML = '<div class="log-entry">No orchestration messages detected yet...</div>';
                }
            }, 2000);
        });

        // Listen for visual editor events
        window.addEventListener('ve-overrides-done', function() {
            addLogEntry('🎉 ve-overrides-done event received', 'step-3');
        });

        window.addEventListener('dyn-sections-loaded', function() {
            addLogEntry('🏗️ dyn-sections-loaded event received', 'step-2');
        });
    </script>

    <!-- Dynamic sections script -->
    <script src="/js/dynamic-sections.js?v=20240530" type="module" defer></script>
</body>
</html>
