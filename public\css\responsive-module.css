/* responsive-module.css */
/* This file contains all responsive styles and media queries from styles2.css */

/* ====================================================================== */
/*                           RESPONSIVE STYLES                           */
/* ====================================================================== */

/* Responsive adjustments for contact buttons */
@media (max-width: 768px) {
    .contact-btn {
        padding: 10px 20px;
        font-size: 0.9em;
        min-width: 100px;
    }
}

@media (max-width: 480px) {
    .contact-btn {
        padding: 8px 16px;
        font-size: 0.85em;
        min-width: 80px;
    }
}

/* Responsive adjustments for dynamic pages - maintain 80% width but adjust padding */
@media (max-width: 900px) {
    .dynamic-page .dyn-block {
        padding: 2.4rem !important; /* Proportionally reduced for mobile (3.2 * 0.75 = 2.4) */
    }

    .dynamic-page .dynamic-section-container {
        width: 80% !important; /* Keep consistent width */
    }
}

@media (max-width: 600px) {
    .dynamic-page .dynamic-section-container {
        width: 95% !important; /* Slightly narrower on very small screens */
    }

    .dynamic-page .dyn-block {
        padding: 1.8rem !important; /* Further reduced for very small screens */
    }
}

/* Responsive adjustments for tutor grid */
@media (max-width: 900px) {
    .tutor-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Single column for portrait on mobile */
@media (max-width: 600px) and (orientation: portrait) {
    .tutor-grid {
        grid-template-columns: 1fr;
    }
}

/* Responsive tweaks */
@media (max-width: 900px) {
    .hero-banner {
        min-height: 300px;
        padding: 2rem 1rem;
    }

    .hero-content {
        padding: 1.5rem;
    }

    .hero-content h1 {
        font-size: 2.2em;
    }

    .hero-content p {
        font-size: 1em;
    }

    .cta-banner {
        min-height: 150px;
        padding: 1.5rem 1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
}

/* Responsive adjustments for overlay cards */
@media (max-width: 700px) {
    .strive-overlay-card, .testimonials-overlay-card, .faq-overlay-card {
        padding: 1.2rem 0.5rem;
        margin: 0.5rem;
        border-radius: 0.8rem;
    }

    .strive-overlay-card h3, .testimonials-overlay-card h3, .faq-overlay-card h3 {
        font-size: 1.3em;
        margin-bottom: 0.8rem;
    }

    .strive-overlay-card p, .testimonials-overlay-card p, .faq-overlay-card p {
        font-size: 0.9em;
        line-height: 1.4;
    }
}

@media (max-width: 900px) {
    .parent-img-row, .tutor-img-row, .pupil-img-row {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .parent-img-row img, .tutor-img-row img, .pupil-img-row img {
        max-width: 80%;
        height: auto;
    }

    .parent-img-row p, .tutor-img-row p, .pupil-img-row p {
        text-align: center;
        max-width: 90%;
    }
}

/* Responsive for <900px: only show left image, reduce size */
@media (max-width: 900px) {
    .zone-gradient-bg {
        padding: 1.2rem 0.5rem;
    }

    .zone-list-row {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .zone-list-row img {
        max-width: 60%;
        height: auto;
    }

    .zone-list-row ul {
        max-width: 90%;
        text-align: center;
    }

    .zone-list-row ul li {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 900px) {
    .newsletter-gradient-bg {
        padding: 1.2rem 0.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .newsletter-gradient-bg h3 {
        font-size: 1.5em;
        margin-bottom: 0.8rem;
    }

    .newsletter-gradient-bg p {
        font-size: 0.95em;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .newsletter-gradient-bg .contact-btn {
        align-self: center;
        max-width: 200px;
    }
}

@media (max-width: 900px) {
    .zone-list-row {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .zone-list-row img {
        max-width: 60%;
        height: auto;
    }

    .zone-list-row ul {
        max-width: 90%;
        text-align: center;
    }
}

@media (max-width: 900px) {
    .pupil-img-list {
        height: 150px;
        width: 150px;
    }
}

/* Responsive navigation for smaller screens */
@media (max-width: 900px) {
    .main-nav ul {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        width: 100%;
        padding: 0;
    }

    .main-nav ul li {
        border-left: none;
        border-bottom: 1px solid #ADD8E6;
        width: 100%;
        text-align: center;
    }

    .main-nav ul li a {
        padding: 12px 10px;
        font-size: 0.95em;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Portrait-specific adjustments */
    @media (orientation: portrait) {
        .main-nav ul {
            grid-template-columns: 1fr 1fr;
        }

        .main-nav ul li a {
            font-size: 0.9em;
            padding: 10px 5px;
        }
    }
}

@media (orientation: portrait) {
    .pupil-img-list-left {
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }

    .pupil-img-list-right {
        right: 0;
        top: 50%;
        transform: translateY(-50%);
    }
}

/* Portrait-specific adjustments for hero banner */
@media (orientation: portrait) {
    .hero-banner {
        width: 92%;
        margin-left: auto;
        margin-right: auto;
    }
}

/* Portrait-specific adjustments for CTA banner */
@media (orientation: portrait) {
    .cta-banner {
        width: 92%;
        margin-left: auto;
        margin-right: auto;
    }
}

/* Responsive testimonials for smaller screens */
@media (max-width: 900px) {
    .testimonials-laced {
        min-height: 500px; /* Increase height to accommodate stacked quotes */
        padding: 1.5rem 1rem;
    }

    .testimonial-quote {
        position: static;
        margin-bottom: 1.5rem;
        max-width: 100%;
        text-align: center;
    }

    .testimonial-quote:last-child {
        margin-bottom: 0;
    }

    /* Portrait-specific adjustments */
    @media (orientation: portrait) {
        .testimonials-laced {
            min-height: 550px; /* Even more height for portrait */
            padding: 1rem 0.5rem;
        }

        .testimonial-quote {
            font-size: 0.9em;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    }
}

/* Portrait-specific border adjustments */
@media (orientation: portrait) {
    .main-nav ul li:nth-child(odd) {
        border-right: 1px solid rgba(173, 216, 230, 0.3) !important;
    }
}

/* Landscape orientation - wider positioning */
@media (orientation: landscape) {
    .pupil-img-list-left {
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
    }

    .pupil-img-list-right {
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
    }
}

@media (max-width: 900px) {
    .tutor-img-list-left,
    .tutor-img-list-right {
        height: 150px;
        width: 150px;
    }

    /* Portrait orientation */
    @media (orientation: portrait) {
        .tutor-img-list-left {
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }

        .tutor-img-list-right {
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    /* Landscape orientation - wider positioning */
    @media (orientation: landscape) {
        .tutor-img-list-left {
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
        }

        .tutor-img-list-right {
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

@media (max-width: 900px) and (orientation: portrait) {
    .parents-box {
        max-width: 98vw;
        margin: 0 auto;
        padding: 1rem;
    }

    .parents-box h2 {
        font-size: 1.8em;
        margin-bottom: 1rem;
    }

    .parents-box p {
        font-size: 0.95em;
        line-height: 1.5;
        margin-bottom: 1rem;
    }
}

@media (max-width: 900px) {
    .parents-box {
        max-width: 98vw;
        margin: 0 auto;
        padding: 1.5rem 1rem;
    }

    .parents-box h2 {
        font-size: 2em;
        margin-bottom: 1.2rem;
    }

    .parents-box p {
        font-size: 1em;
        line-height: 1.6;
        margin-bottom: 1.2rem;
    }

    /* Portrait-specific styles */
    @media (orientation: portrait) {
        .parent-stack-left {
            left: calc(50% - 10px); /* Centered but shifted 10px to the left */
        }
    }

    /* Landscape-specific styles */
    @media (orientation: landscape) {
        .parent-stack-left {
            left: -10px; /* Keep the 10px left offset */
        }
    }
}

/* Center legoRight.png below section in portrait mode only */
@media (orientation: portrait) {
    .tutor-img-list-right {
        display: block !important;
        position: static !important;
        margin: 2rem auto 0 auto !important;
        left: auto !important;
        right: auto !important;
        top: auto !important;
        transform: none !important;
        text-align: center !important;
    }
}

@media (max-width: 900px) {
    .dyn-block {
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .dyn-block h2 {
        font-size: 1.8em;
        margin-bottom: 1rem;
    }

    .dyn-block p {
        font-size: 0.95em;
        line-height: 1.5;
        margin-bottom: 1rem;
    }
}

/* Portrait-specific adjustments for dynamic sections alignment */
@media (max-width: 1200px) and (orientation: portrait) {
    #dynamicSections,
    #dynamicSectionsTop,
    #dynamicSectionsMiddle,
    #dynamicSectionsBottom {
        width: 95% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        padding: 0 !important;
    }

    .dynamic-section-container {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 1rem !important;
    }

    .dyn-block {
        width: 100% !important;
        margin: 1rem 0 !important;
        padding: 1.5rem !important;
    }
}

/* Landscape-specific adjustments for dynamic sections alignment */
@media (max-width: 1200px) and (orientation: landscape) {
    #dynamicSections,
    #dynamicSectionsTop,
    #dynamicSectionsMiddle,
    #dynamicSectionsBottom {
        width: 90% !important;
        margin-left: auto !important;
        margin-right: auto !important;
        padding: 0 !important;
    }

    .dynamic-section-container {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 1.5rem !important;
    }

    .dyn-block {
        width: 100% !important;
        margin: 1.5rem 0 !important;
        padding: 2rem !important;
    }
}

/* ====================================================================== */
/*                    RESTRICTED VIEWPORT STYLES                        */
/* ====================================================================== */
@media (max-width: 1200px) and (orientation: portrait) {
  /*
   * The original 100vw width + –50vw margins push the section's
   * background beyond the viewport edges. In restricted portrait mode,
   * we want the section to span the full width but stay within bounds.
   */
  .full-width-section {
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
    margin-right: calc(-50vw + 50%) !important;
    padding-left: 2rem !important;
    padding-right: 2rem !important;
  }
}

/* Landscape-specific adjustments for restricted viewports
   ------------------------------ */
@media (max-width:1200px) and (orientation:landscape){

  /* 1️⃣  Keep the background full-width but cancel the old trickery */
  .full-width-section {
    width: 100vw !important;
    margin-left: calc(-50vw + 50%) !important;
    margin-right: calc(-50vw + 50%) !important;
    padding-left: 3rem !important;
    padding-right: 3rem !important;
  }

  /* 2️⃣  Ensure content inside doesn't overflow */
  .full-width-section > * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* 3️⃣  Specific adjustments for common elements */
  .full-width-section h1,
  .full-width-section h2,
  .full-width-section h3,
  .full-width-section p,
  .full-width-section ul,
  .full-width-section ol {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  /* 4️⃣  Image adjustments */
  .full-width-section img {
    max-width: 100% !important;
    height: auto !important;
  }

  /* 5️⃣  Button and form adjustments */
  .full-width-section .contact-btn,
  .full-width-section button,
  .full-width-section input,
  .full-width-section textarea {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
}
