<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <title>Tutors Alliance Scotland - Helping all Scottish children grow</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/css/footer-module.css">
    <link rel="stylesheet" href="/css/button-module.css">
    <link rel="stylesheet" href="/css/typography-module.css">
    <link rel="stylesheet" href="/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <script src="/responsive-helper.js"></script>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/js/nav-loader.js" defer=""></script>
    <script src="/js/dynamic-nav.js" defer=""></script>
</head>
<body class="tutor-directory-page restricted-viewport" style="background-color: #F0F8FF;" data-page="tutorDirectory" data-dyn-manual="true">
    <!-- Shared banner/header -->
    <header>
        <h1 data-ve-block-id="c6483d52-334f-4129-95c8-677e5b5220ab">Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a href="index.html" class="banner-login-link login-box" data-ve-block-id="9f3e4408-ce25-4903-a446-0cc4692c547e">Home</a>
            <a href="login.html?role=admin" class="banner-login-link login-box" data-ve-block-id="d17c8e40-a42f-4df2-afc3-ee3620cc64e3">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->


    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            <!-- JS will populate tutor names/subjects here -->
        </div>
    </div>


    <main>
        <div class="content-flex-wrapper">
            <!-- LEFT COLUMN: Shield + Ribbons -->
            <div class="left-col">
                <img src="/images/centralShield.png" alt="Large STA Shield" class="main-shield" id="imageShield" data-ve-block-id="e4873d7e-48d4-404c-a7ec-f794da331e9f">
                <img src="/images/bannerWithRibbons.png" alt="Banner Ribbon" class="main-ribbons" id="imageBanner" data-ve-block-id="52cf130f-c993-43a5-b372-f539272a8577">
            </div>

            <!-- RIGHT COLUMN: heading + about-us text -->
            <div class="right-col">

                <!-- ✦ TOP -->
                <section id="dynamicSectionsTop" class="dynamic-section-container" data-ve-section-id="dynamicSectionsTop"></section>


                <div class="about-us-landing" id="aboutUsLanding">
                    <h1 class="mission-statement" style="color: #0057B7;" data-ve-block-id="3552bdff-4ddd-41b2-bb6a-56981b7725a3">Find a Tutor</h1>
                    <p data-ve-block-id="c496e739-1ae7-4489-8c18-cbb30d0e6696">
                        Use this search form to find tutors by subject and location. All tutors are fully
                        qualified teachers, covered by statutory safeguarding guidelines.
                    </p>
                </div>

                <style>
                    /* Center heading in portrait orientation on restricted viewport devices */
                    @media (max-width: 900px) and (orientation: portrait) {
                        .mission-statement {
                            text-align: center !important;
                            margin-left: auto !important;
                            margin-right: auto !important;
                        }

                        .about-us-landing p {
                            text-align: center !important;
                        }
                    }
                </style>

                <div class="form-container" style="width: 80%; max-width: 500px; background-color: #fff; border-radius: 1rem; padding: 2rem; box-shadow: 0 2px 18px rgba(0,87,183,0.05); margin: 2rem auto;">
                    <h2 style="color: #001B44; margin-bottom: 1.5rem; text-align: center;" data-ve-block-id="ec0294dd-32e4-41e3-ab6c-e70d1fa07654">Tutor Finder</h2>
                    <form id="tutorFinderForm" action="/tutors" method="GET" style="display: flex; flex-direction: column; gap: 1.5rem; align-items: center;">
                        <div style="width: 100%; text-align: center;">
                            <label for="subject" style="display: block; margin-bottom: 0.5rem; font-weight: bold; color: #333; text-align: center;">Subject</label>
                            <select name="subject" id="subject" style="width: 80%; max-width: 300px; padding: 0.8rem; border: 1px solid #ddd; border-radius: 0.5rem; font-size: 1rem;">
                                <option value="mathematics">Mathematics</option>
                                <option value="english">English</option>
                            </select>
                        </div>

                        <div style="width: 100%; text-align: center;">
                            <label for="mode" style="display: block; margin-bottom: 0.5rem; font-weight: bold; color: #333; text-align: center;">Location</label>
                            <select name="mode" id="mode" style="width: 80%; max-width: 300px; padding: 0.8rem; border: 1px solid #ddd; border-radius: 0.5rem; font-size: 1rem;">
                                <option value="online">Online</option>
                                <option value="in-person">In Person</option>
                            </select>
                        </div>

                        <div id="postcodeContainer" style="display: none; width: 100%; text-align: center;">
                            <label for="postcode" style="display: block; margin-bottom: 0.5rem; font-weight: bold; color: #333; text-align: center;">Postcode</label>
                            <input type="text" name="postcode" id="postcode" placeholder="Enter your postcode" style="width: 80%; max-width: 300px; padding: 0.8rem; border: 1px solid #ddd; border-radius: 0.5rem; font-size: 1rem;">
                        </div>

                        <button type="submit" class="button aurora" style="width: 80%; max-width: 300px; background-color: #0057B7; color: white; padding: 1rem; border: none; border-radius: 0.5rem; font-size: 1.1rem; font-weight: bold; cursor: pointer; margin-top: 0.5rem;">Find Tutors</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- ✦ MIDDLE -->
        <section id="dynamicSectionsMiddle" class="dynamic-section-container" data-ve-section-id="dynamicSectionsMiddle" hidden=""></section>

        

        <!-- ✦ BOTTOM -->
        <section id="dynamicSections" class="dynamic-section-container" data-ve-section-id="dynamicSections" hidden=""></section>

    </main>
        <script>
            function goToLogin(role) {
                window.location.href = `login.html?role=${encodeURIComponent(role)}`;
            }
        </script>

        <script type="module" src="/js/visual-editor-v2.js?v=20250101-CACHE-BUST&t=1735747200" defer=""></script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                initRollingBanner();
            });


            window.addEventListener('DOMContentLoaded', () => {
                // After the heading's animation delay (say 1.5s) plus a little buffer:
                setTimeout(() => {
                    document.querySelectorAll('.fade-later').forEach(el => {
                        el.classList.add('fade-in');
                    });
                }, 1500); // or 2000 if you want a bit more buffer

                // Contact button functionality is now handled by the enhanced implementation in api/tutors.js
            });




            document.getElementById('mode').addEventListener('change', function () {
                const postcodeContainer = document.getElementById('postcodeContainer');
                if (this.value === 'in-person') {
                    postcodeContainer.style.display = 'block';
                } else {
                    postcodeContainer.style.display = 'none';
                }
            });

            document.getElementById('tutorFinderForm').addEventListener('submit', function (event) {
                event.preventDefault();

                const subject = document.getElementById('subject').value;
                const mode = document.getElementById('mode').value;
                let postcode = document.getElementById('postcode').value;

                if (postcode) {
                    postcode = postcode.toUpperCase();
                }

                let queryParams = `?subject=${encodeURIComponent(subject)}&mode=${encodeURIComponent(mode)}`;
                if (mode === "in-person" && postcode.trim() !== "") {
                    queryParams += `&postcode=${encodeURIComponent(postcode)}`;
                }

                window.location.href = `/tutors/search${queryParams}`;
            });
        </script>
        <script>
            // Fade-in animation for sections
            const fadeEls = document.querySelectorAll('.fade-in-on-scroll');

            // Set initial styles
            fadeEls.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
            });

            // Create the Intersection Observer
            const observer = new IntersectionObserver((entries, obs) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Fade it in
                        entry.target.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';

                        // Once triggered, stop observing so it doesn't re-animate if user scrolls away
                        obs.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 }); // threshold=0.1 => trigger at 10% visibility

            // Observe each fadeEl
            fadeEls.forEach(el => observer.observe(el));
        </script>

        <!-- Load the dynamic sections script -->
        <script src="/js/dynamic-sections.js?v=20240530" type="module" defer=""></script>



<!-- Visual Editor Templates -->
<template id="ve-editor-modal-template">
    <div id="editor-modal" class="ve-modal-container">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Edit Content</h3>
                <button id="close-modal" class="close-btn" aria-label="Close modal">×</button>
            </div>
            <div class="modal-body">
                <form id="content-form" onsubmit="return false;">
                    <div class="form-group">
                        <label for="content-type">Content Type:</label>
                        <select id="content-type" disabled="">
                            <option value="text">Text</option>
                            <option value="html">HTML</option>
                            <option value="image">Image</option>
                            <option value="link">Link</option>
                        </select>
                    </div>
                    <div class="form-group" id="text-group">
                        <label for="content-text">Text Content:</label>
                        <textarea id="content-text" rows="4"></textarea>
                    </div>
                    <div class="form-group" id="html-group">
                        <label for="content-html">HTML Content:</label>
                        <textarea id="content-html" rows="6"></textarea>
                    </div>
                    <div class="form-group" id="image-group">
                        <label for="content-image">Image URL:</label>
                        <div class="image-input-group">
                            <input type="url" id="content-image">
                            <button type="button" id="browse-btn" class="btn btn-secondary">Browse Images</button>
                        </div>
                        <div id="image-preview" style="display: none;"><img src="" alt="Preview" style="max-width: 200px; max-height: 200px;"></div>
                        <div class="upload-section">
                            <label for="image-upload">Or upload a new image:</label>
                            <input type="file" id="image-upload" accept="image/*">
                            <button type="button" id="upload-btn" class="btn btn-secondary">Upload Image</button>
                            <div id="upload-progress" style="display: none;">
                                <div class="progress-bar"><div class="progress-fill"></div></div>
                                <span class="progress-text">Uploading...</span>
                            </div>
                        </div>
                        <label for="image-alt">Alt Text:</label>
                        <input type="text" id="image-alt">
                    </div>
                    <div class="form-group" id="link-group">
                        <label for="link-url">Link URL:</label>
                        <input type="url" id="link-url">
                        <label for="link-text">Link Text:</label>
                        <input type="text" id="link-text">
                        <div class="form-check mt-2">
                            <input type="checkbox" id="link-is-button" class="form-check-input">
                            <label for="link-is-button" class="form-check-label">Style as button</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                        <button type="button" id="save-btn" class="btn btn-primary">Save Changes</button>
                        <button type="button" id="restore-btn" class="btn btn-warning">Restore Original</button>
                    </div>
                </form>
            </div>
        </div>
        <div id="image-browser" class="image-browser" style="display: none;"></div>
    </div>
</template>

<template id="ve-image-browser-template">
    <div class="image-browser-header">
        <h4>Browse Images</h4>
        <button type="button" id="close-browser" class="close-btn" aria-label="Close image browser">×</button>
    </div>
    <div class="image-browser-content">
        <div class="image-browser-toolbar">
            <input type="text" id="image-search" placeholder="Search images..." class="form-control">
            <select id="image-sort" class="form-control">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name</option>
            </select>
        </div>
        <div id="image-grid" class="image-grid">
            <div class="loading-spinner"></div>
        </div>
        <div id="image-pagination" class="image-pagination">
            <button type="button" id="prev-page" class="btn btn-secondary" disabled="">Previous</button>
            <span id="page-info">Page 1</span>
            <button type="button" id="next-page" class="btn btn-secondary">Next</button>
        </div>
    </div>
</template>

    <!-- SOCIAL ICONS FOOTER -->
    <footer class="site-footer fade-in-section">
        <div class="footer-icons">
            <a href="#" aria-label="Instagram" data-ve-block-id="cc4c0365-e903-4f74-a123-b9eb051f6d52"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="Facebook" data-ve-block-id="cc24258d-6d03-4357-8abc-dfdd87f9c392"><i class="fab fa-facebook-f"></i></a>
        </div>
    </footer>

    <!-- STATIC BOTTOM FOOTER -->
    <footer class="static-footer">
        <div class="static-footer-container">
            <div class="static-footer-left">
                <h4 data-ve-block-id="6b4b000d-d76d-4106-9c17-ef0a7cf857bf">Extra Information</h4>
                <ul>
                    <li data-ve-block-id="li-footer-governance-idx"><a href="tutoring-standards.html" data-ve-block-id="2de28fd6-a5c4-4f0b-b793-42f5ffd7dad0">The TAS Way: Governance and Guidance</a></li>
                    <li data-ve-block-id="li-footer-faq-idx"><a href="faq.html" data-ve-block-id="0731b2a7-9365-4c12-80e7-9d6830e24f93">FAQ's</a></li>
                    <li data-ve-block-id="li-footer-privacy-idx"><a href="privacy-policy.html" data-ve-block-id="************************************">Privacy Policy</a></li>
                    <li data-ve-block-id="li-footer-safeguarding-idx"><a href="safeguarding-policy.html" data-ve-block-id="699b2182-1a80-4e3a-90e8-7f4b270c63ad">Safeguarding Policy</a></li>
                    <li data-ve-block-id="li-footer-terms-idx"><a href="terms-and-conditions.html" data-ve-block-id="76e69afc-6427-4b33-952c-5e573237f25d">Terms and Conditions</a></li>
                </ul>
                <div class="static-footer-copyright">
                    <p data-ve-block-id="be58a2f6-83cf-4fdc-9908-ce6aaff902a2">ALL RIGHTS RESERVED © Tutors Alliance Scotland 2025</p>
                </div>
                <div class="static-footer-credits">
                    <p data-ve-block-id="32306cdb-8f03-42e7-970f-2dbb3f138968">Website by <a href="#" target="_blank" data-ve-block-id="b77338ab-95de-4046-bd6b-2f79ffba40f5">Tutors Alliance Scotland</a></p>
                </div>
            </div>
            <div class="static-footer-right">
                <div class="website-url">
                    <p data-ve-block-id="28739c2d-b4a4-46a5-bef4-b758c4600585">www.tutorsalliancescotland.org.uk</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

</body></html>