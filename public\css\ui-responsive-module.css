/* ui-responsive-module.css */
/* This file contains responsive styles for UI elements: contact buttons, hero/CTA banners */
/*
 * CROSS-MODULE DEPENDENCIES:
 * - Extends: styles2.css base responsive patterns
 * - Coordinates with: layout-module.css for container widths
 * - Uses: Same max-width pattern as main content containers
 *
 * LOAD ORDER: Not in standard load order - included separately where needed
 * PURPOSE: Responsive adjustments for specific UI components
 */

/* ====================================================================== */
/*                    CONTACT BUTTON RESPONSIVE STYLES                  */
/* ====================================================================== */

/* Responsive adjustments for contact buttons */
@media (max-width: 768px) {
    .contact-btn {
        padding: 10px 20px;
        font-size: 0.9em;
        min-width: 100px;
    }
}

@media (max-width: 480px) {
    .contact-btn {
        padding: 8px 16px;
        font-size: 0.85em;
        min-width: 90px;
        display: block;
        margin: 0 auto;
        max-width: 200px;
    }

    .tutor-contact {
        margin: 10px 0;
    }
}

/* ====================================================================== */
/*                    HERO & CTA BANNER RESPONSIVE STYLES              */
/* ====================================================================== */

/* Responsive tweaks for hero and CTA banners */
@media (max-width: 900px) {
    .hero-banner {
        min-height: 300px;
        padding: 2rem 1rem;
    }

    .hero-content {
        padding: 1.5rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .cta-banner {
        min-height: 180px;
        padding: 1.5rem 1rem;
    }
}

/* Portrait-specific adjustments for hero banner */
/* Specificity: 0,0,1,0 + media query - Overrides base hero-banner styles */
@media (max-width: 1200px) and (orientation: portrait) {
    .hero-banner {
        max-width: 1080px; /* Matches layout-module.css container pattern */
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        padding-left: 1rem;
        padding-right: 1rem;
        box-sizing: border-box;
    }
}

/* Portrait-specific adjustments for CTA banner */
@media (max-width: 1200px) and (orientation: portrait) {
    .cta-banner {
        max-width: 1080px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        padding-left: 1rem;
        padding-right: 1rem;
        box-sizing: border-box;
    }
}

/* Landscape-specific CTA banner adjustment */
@media (orientation: landscape) {
    .cta-banner {
        transform: translateX(-7px) !important;
    }
}
