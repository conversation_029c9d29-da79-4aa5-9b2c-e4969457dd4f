<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Context Isolation Test - Visual Editor</title>
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/css/footer-module.css">
    <link rel="stylesheet" href="/css/button-module.css">
    <link rel="stylesheet" href="/css/typography-module.css">
    <link rel="stylesheet" href="/css/animation-module.css">
    <link rel="stylesheet" href="/editor.css">
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .context-label {
            font-weight: bold;
            color: #0057b7;
            margin-bottom: 10px;
        }
        .test-links {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .test-links a {
            padding: 8px 16px;
            background: #f0f0f0;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
        }
        .test-links a:hover {
            background: #e0e0e0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body data-page="test-context-isolation">
    
    <!-- HEADER SECTION -->
    <header>
        <div class="test-section">
            <div class="context-label">HEADER CONTEXT</div>
            <h1 data-ve-block-id="header-title">Test Header Title</h1>
            <div class="test-links">
                <a href="/contact.html" data-ve-block-id="header-contact">Contact</a>
                <a href="/about-us.html" data-ve-block-id="header-about">About</a>
                <a href="/login.html" data-ve-block-id="header-login">Login</a>
                <a href="/duplicate-test.html" data-ve-block-id="header-duplicate">Duplicate URL Test</a>
            </div>
        </div>
    </header>

    <!-- NAVIGATION (should be excluded) -->
    <nav class="main-nav">
        <div class="test-section">
            <div class="context-label">NAVIGATION CONTEXT (EXCLUDED)</div>
            <div class="test-links">
                <a href="/contact.html">Contact (Nav)</a>
                <a href="/about-us.html">About (Nav)</a>
                <a href="/duplicate-test.html">Duplicate URL Test (Nav)</a>
            </div>
        </div>
    </nav>

    <!-- MAIN CONTENT -->
    <main>
        <div class="warning">
            <strong>Context Isolation Test Page</strong><br>
            This page tests that header, main, and footer links with identical URLs are properly isolated.
            When editing is enabled, you should be able to edit links in each context independently.
        </div>

        <section data-ve-section-id="main-content">
            <div class="test-section">
                <div class="context-label">MAIN CONTEXT</div>
                <h2 data-ve-block-id="main-title">Main Content Title</h2>
                <p data-ve-block-id="main-paragraph">This is a test paragraph in the main content area.</p>
                <div class="test-links">
                    <a href="/contact.html" data-ve-block-id="main-contact">Contact</a>
                    <a href="/about-us.html" data-ve-block-id="main-about">About</a>
                    <a href="/login.html" data-ve-block-id="main-login">Login</a>
                    <a href="/duplicate-test.html" data-ve-block-id="main-duplicate">Duplicate URL Test</a>
                </div>
                <img src="/images/favicon.png" alt="Test Image" data-ve-block-id="main-image">
            </div>
        </section>

        <section data-ve-section-id="secondary-content">
            <div class="test-section">
                <div class="context-label">SECONDARY MAIN CONTEXT</div>
                <h3 data-ve-block-id="secondary-title">Secondary Section</h3>
                <p data-ve-block-id="secondary-paragraph">Another paragraph to test section isolation.</p>
                <div class="test-links">
                    <a href="/services.html" data-ve-block-id="secondary-services">Services</a>
                    <a href="/portfolio.html" data-ve-block-id="secondary-portfolio">Portfolio</a>
                </div>
            </div>
        </section>
    </main>

    <!-- FOOTER SECTION -->
    <footer>
        <div class="test-section">
            <div class="context-label">FOOTER CONTEXT</div>
            <div class="test-links">
                <a href="/contact.html" data-ve-block-id="footer-contact">Contact</a>
                <a href="/about-us.html" data-ve-block-id="footer-about">About</a>
                <a href="/privacy.html" data-ve-block-id="footer-privacy">Privacy</a>
                <a href="/duplicate-test.html" data-ve-block-id="footer-duplicate">Duplicate URL Test</a>
            </div>
            <p data-ve-block-id="footer-copyright">© 2025 Test Site</p>
        </div>
    </footer>

    <!-- Test Results Display -->
    <div id="test-results" style="position: fixed; top: 10px; right: 10px; background: white; border: 2px solid #333; padding: 15px; border-radius: 8px; max-width: 300px; font-family: monospace; font-size: 12px; z-index: 10000;">
        <h4 style="margin: 0 0 10px 0;">Context Test Results</h4>
        <div id="test-output">Click "Run Context Test" to verify isolation</div>
        <button onclick="runContextTest()" style="margin-top: 10px; padding: 5px 10px;">Run Context Test</button>
        <button onclick="testVisualIndicators()" style="margin-top: 5px; padding: 5px 10px; background: #4CAF50; color: white; border: none; border-radius: 3px;">Test Visual Indicators</button>
    </div>

    <!-- Visual Editor Scripts -->
    <script type="module">
        import { apiService } from '/js/editor/api-service.js';
        import { editorState } from '/js/editor/editor-state.js';
        import { overrideEngine } from '/js/editor/override-engine.js';
        import { UIManager } from '/js/editor/ui-manager.js';

        // Initialize visual editor components
        const uiManager = new UIManager({
            onToggle: () => console.log('Toggle edit mode'),
            onEdit: (el) => console.log('Edit element:', el),
            onSave: () => console.log('Save'),
            onPreview: () => console.log('Preview'),
            onRestore: () => console.log('Restore'),
            onUpload: () => console.log('Upload'),
            getType: el => overrideEngine.getElementType(el),
            getOriginalContent: (el, type) => overrideEngine.getOriginalContent(el, type),
        });

        // Make components available globally for testing
        window.testComponents = { uiManager, overrideEngine, editorState };
        
        // Initialize
        uiManager.initialize();
        
        console.log('Context isolation test page loaded');
    </script>

    <!-- Load comprehensive test script -->
    <script src="/test-context-isolation-script.js"></script>

    <script>
        function runContextTest() {
            const output = document.getElementById('test-output');
            const results = [];
            
            try {
                // Test context detection
                const headerLink = document.querySelector('header a[href="/contact.html"]');
                const mainLink = document.querySelector('main a[href="/contact.html"]');
                const footerLink = document.querySelector('footer a[href="/contact.html"]');
                
                if (!headerLink || !mainLink || !footerLink) {
                    results.push('❌ Missing test links');
                    output.innerHTML = results.join('<br>');
                    return;
                }
                
                // Test selector generation
                const headerSelector = window.testComponents.overrideEngine.getStableSelector(headerLink, 'link');
                const mainSelector = window.testComponents.overrideEngine.getStableSelector(mainLink, 'link');
                const footerSelector = window.testComponents.overrideEngine.getStableSelector(footerLink, 'link');
                
                results.push(`Header: ${headerSelector}`);
                results.push(`Main: ${mainSelector}`);
                results.push(`Footer: ${footerSelector}`);
                
                // Verify they're different
                const selectors = [headerSelector, mainSelector, footerSelector];
                const unique = new Set(selectors);
                
                if (unique.size === 3) {
                    results.push('✅ All selectors unique');
                } else {
                    results.push('❌ Selector collision detected');
                }
                
                // Test context detection
                const headerContext = window.testComponents.overrideEngine.getElementContext(headerLink);
                const mainContext = window.testComponents.overrideEngine.getElementContext(mainLink);
                const footerContext = window.testComponents.overrideEngine.getElementContext(footerLink);
                
                results.push(`Contexts: ${headerContext}, ${mainContext}, ${footerContext}`);
                
                if (headerContext === 'header' && mainContext === 'main' && footerContext === 'footer') {
                    results.push('✅ Context detection working');
                } else {
                    results.push('❌ Context detection failed');
                }
                
            } catch (error) {
                results.push(`❌ Error: ${error.message}`);
            }
            
            output.innerHTML = results.join('<br>');
        }

        function testVisualIndicators() {
            const output = document.getElementById('test-output');

            // Enable edit mode to show overlays
            if (window.testComponents && window.testComponents.editorState) {
                window.testComponents.editorState.setEditMode(true);
                output.innerHTML = '✅ Edit mode enabled!<br>Hover over elements to see context labels.<br>Click elements to see context badges in modal.';
            } else {
                output.innerHTML = '❌ Test components not available';
            }
        }
    </script>

    <!-- Edit Mode Toggle Button -->
    <button id="edit-mode-toggle" style="position: fixed; bottom: 20px; right: 20px; padding: 10px 20px; background: #0057b7; color: white; border: none; border-radius: 4px; cursor: pointer; z-index: 9999;">
        Edit Mode
    </button>

</body>
</html>
