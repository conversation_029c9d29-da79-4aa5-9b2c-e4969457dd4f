<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Section Reordering - TAS</title>
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/css/footer-module.css">
    <link rel="stylesheet" href="/css/button-module.css">
    <link rel="stylesheet" href="/css/typography-module.css">
    <link rel="stylesheet" href="/header-banner.css">
    <style>
        /* Test page specific styles */
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h2 {
            margin-top: 0;
            color: #333;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div id="rolling-banner" class="rolling-banner">
        <div class="rolling-content">
            <span id="tutorBanner">Loading news...</span>
        </div>
    </div>

    <header>
        <div class="header-container">
            <div class="logo-section">
                <img src="/images/centralShield.png" alt="TAS Logo" class="logo" data-ve-block-id="995bd3c5-3068-4b72-8be9-b6d02d6680d3">
                <div class="logo-text">
                    <h1 data-ve-block-id="985374b1-bfd5-48af-90d5-635181d86f60">Tutors Alliance Scotland</h1>
                    <p data-ve-block-id="2c32019a-ef6f-42d7-a3ed-4287ead1e02f">Professional Membership Organisation</p>
                </div>
            </div>
        </div>
    </header>

    <nav id="main-nav">
        <!-- Navigation will be loaded dynamically -->
    </nav>

    <main>
        <div class="test-info">
            <h1 data-ve-block-id="277a59c6-e67b-4c49-941d-5689b579ef8e">🧪 Section Reordering Test Page</h1>
            <p data-ve-block-id="099480ed-2ab8-4461-8501-faa6cde48de7"><strong>Instructions:</strong> If you're logged in as an admin, click the "Edit Mode" toggle to enable section reordering. You should see drag handles (⇅) appear on each section. Drag sections to reorder them!</p>
        </div>

        <!-- Test sections with stable IDs -->
        <section class="test-section fade-in-section" data-ve-section-id="section-1">
            <h2 data-ve-block-id="dff15162-40af-4ada-bec9-87436e7a8bbc">🥇 Section 1 - First</h2>
            <p data-ve-block-id="ca7dee87-e124-4fae-97f1-fd5530aa5db2">This is the first test section. It should be draggable when in edit mode.</p>
            <p data-ve-block-id="3d7f1046-61f3-4dc5-a492-71ba0d8b2b05">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        </section>

        <section class="test-section fade-in-section" data-ve-section-id="section-2">
            <h2 data-ve-block-id="15508aa2-8384-4269-b6f1-a938aa5504a3">🥈 Section 2 - Second</h2>
            <p data-ve-block-id="80cd1c7f-20b7-46b9-87ec-4ebd4d5e4d49">This is the second test section. Try dragging it above or below other sections!</p>
            <p data-ve-block-id="736dcbce-354f-434f-a752-ed8ef4c932e9">Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        </section>

        <section class="test-section fade-in-section" data-ve-section-id="section-3">
            <h2 data-ve-block-id="4a76f5f3-ab04-4583-ba9d-98b4a5ac2b62">🥉 Section 3 - Third</h2>
            <p data-ve-block-id="26b0e7b0-5092-4406-b152-048e3e25b2e1">This is the third test section. The order should persist after page reload.</p>
            <p data-ve-block-id="2900e44c-16d7-4810-8c7e-30283031e649">Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        </section>

        <section class="test-section fade-in-section" data-ve-section-id="section-4">
            <h2 data-ve-block-id="917ef52a-2897-42ea-a8db-3d50826d3f1b">🏅 Section 4 - Fourth</h2>
            <p data-ve-block-id="077f1c52-72a9-4914-a516-f0f0adafea5e">This is the fourth test section. Each section has a unique data-ve-section-id.</p>
            <p data-ve-block-id="a1c12f75-8d58-4d43-812f-c6d11e924161">Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </section>

        <section class="test-section fade-in-section" data-ve-section-id="section-5">
            <h2 data-ve-block-id="b5268612-dd11-40f4-88ec-c8397ff5a2de">🎯 Section 5 - Fifth</h2>
            <p data-ve-block-id="b3cbc651-a09a-40cf-bf92-7e59efa15203">This is the fifth and final test section. The reordering should work smoothly with visual feedback.</p>
            <p data-ve-block-id="23a5a25f-8264-468c-a351-a0dddc03bfb0">Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
        </section>

        <!-- Dynamic sections container -->
        <section id="dynamicSections" class="dynamic-section-container"></section>
    </main>

    <!-- Footer -->
    <footer class="static-footer">
        <div class="footer-content">
            <p data-ve-block-id="015ee6f4-9c05-4b4c-953b-01cca3e2b8f5">© 2024 Tutors Alliance Scotland. All rights reserved.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/rolling-banner.js"></script>
    <script src="/js/nav-loader.js"></script>
    <script src="/js/dynamic-sections.js"></script>
    <script type="module" src="/js/visual-editor-v2.js?v=20250101-CACHE-BUST&t=1735747800"></script>
    
    <script>
        // Test page specific JavaScript
        console.log('Test page loaded - checking for section reordering functionality');
        
        // Log section order on page load
        const sections = document.querySelectorAll('[data-ve-section-id]');
        console.log('Initial section order:', Array.from(sections).map(s => s.dataset.veSectionId));
        
        // Add some visual feedback for testing
        document.addEventListener('DOMContentLoaded', () => {
            const testInfo = document.querySelector('.test-info');
            if (testInfo) {
                const orderDisplay = document.createElement('div');
                orderDisplay.id = 'order-display';
                orderDisplay.style.marginTop = '10px';
                orderDisplay.style.padding = '10px';
                orderDisplay.style.background = '#fff3cd';
                orderDisplay.style.borderRadius = '4px';
                orderDisplay.innerHTML = '<strong>Current Order:</strong> <span id="current-order"></span>';
                testInfo.appendChild(orderDisplay);
                
                // Update order display
                function updateOrderDisplay() {
                    const currentSections = document.querySelectorAll('[data-ve-section-id]');
                    const order = Array.from(currentSections).map(s => s.dataset.veSectionId);
                    document.getElementById('current-order').textContent = order.join(' → ');
                }
                
                updateOrderDisplay();
                
                // Watch for changes
                const observer = new MutationObserver(updateOrderDisplay);
                observer.observe(document.querySelector('main'), { 
                    childList: true, 
                    subtree: true 
                });
            }
        });
    </script>
<!-- Visual Editor Templates -->
<template id="ve-editor-modal-template">
    <div id="editor-modal" class="ve-modal-container">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Edit Content</h3>
                <button id="close-modal" class="close-btn" aria-label="Close modal">×</button>
            </div>
            <div class="modal-body">
                <form id="content-form" onsubmit="return false;">
                    <div class="form-group">
                        <label for="content-type">Content Type:</label>
                        <select id="content-type" class="form-control" disabled="">
                            <option value="text">Text</option>
                            <option value="html">HTML</option>
                            <option value="image">Image</option>
                            <option value="link">Link</option>
                        </select>
                    </div>
                    <div class="form-group" id="text-group">
                        <label for="content-text">Text Content:</label>
                        <textarea id="content-text" class="form-control" rows="8" placeholder="Enter your text content here..."></textarea>
                        
                        <!-- Button Management for Text Elements -->
                        <div class="text-button-management">
                            <h4>Add Button</h4>
                            <p class="help-text">Add an aurora-style button at the end of this text element</p>
                            
                            <div id="text-buttons-list" class="text-buttons-list">
                                <!-- Existing buttons will be listed here -->
                            </div>
                            
                            <div class="button-form" id="new-button-form" style="display: none;">
                                <div class="form-group">
                                    <label for="new-button-text">Button Text:</label>
                                    <input type="text" id="new-button-text" class="form-control" placeholder="Enter button text">
                                </div>
                                <div class="form-group">
                                    <label for="new-button-url">Button URL:</label>
                                    <input type="url" id="new-button-url" class="form-control" placeholder="https://example.com">
                                </div>
                                <div class="button-form-actions">
                                    <button type="button" id="save-new-button" class="btn btn-primary">Add Button</button>
                                    <button type="button" id="cancel-new-button" class="btn btn-secondary">Cancel</button>
                                </div>
                            </div>
                            
                            <button type="button" id="add-text-button" class="btn btn-secondary">+ Add Button</button>
                        </div>
                    </div>
                    <div class="form-group" id="html-group">
                        <label for="content-html">HTML Content:</label>
                        <textarea id="content-html" class="form-control" rows="10" placeholder="Enter your HTML content here..."></textarea>
                    </div>
                    <div class="form-group" id="image-group">
                        <label for="content-image">Image URL:</label>
                        <div class="image-input-group">
                            <input type="url" id="content-image" class="form-control" placeholder="Enter image URL or browse/upload below">
                            <button type="button" id="browse-btn" class="btn btn-secondary">Browse Images</button>
                        </div>
                        <div id="image-preview" style="display: none;"><img src="" alt="Preview" style="max-width: 200px; max-height: 200px;"></div>
                        <div class="upload-section">
                            <label for="image-upload">Or upload a new image:</label>
                            <input type="file" id="image-upload" accept="image/*" class="form-control">
                            <button type="button" id="upload-btn" class="btn btn-secondary">Upload Image</button>
                            <div id="upload-progress" style="display: none;">
                                <div class="progress-bar"><div class="progress-fill"></div></div>
                                <span class="progress-text">Uploading...</span>
                            </div>
                        </div>
                        <label for="image-alt">Alt Text:</label>
                        <input type="text" id="image-alt" class="form-control" placeholder="Describe the image for accessibility">
                    </div>
                    <div class="form-group" id="link-group">
                        <label for="link-url">Link URL:</label>
                        <input type="url" id="link-url" class="form-control" placeholder="https://example.com">
                        <label for="link-text">Link Text:</label>
                        <input type="text" id="link-text" class="form-control" placeholder="Enter the text to display">
                        <div class="form-check">
                            <input type="checkbox" id="link-is-button" class="form-check-input">
                            <label for="link-is-button" class="form-check-label">Style as button</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                        <button type="button" id="save-btn" class="btn btn-primary">Save Changes</button>
                        <button type="button" id="restore-btn" class="btn btn-warning">Restore Original</button>
                    </div>
                </form>
            </div>
        </div>
        <div id="image-browser" class="image-browser" style="display: none;"></div>
    </div>
</template>

<template id="ve-image-browser-template">
    <div class="image-browser-header">
        <h4>Browse Images</h4>
        <button type="button" id="close-browser" class="close-btn" aria-label="Close image browser">×</button>
    </div>
    <div class="image-browser-content">
        <div class="image-browser-toolbar">
            <input type="text" id="image-search" placeholder="Search images..." class="form-control">
            <select id="image-sort" class="form-control">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name</option>
            </select>
        </div>
        <div id="image-grid" class="image-grid">
            <div class="loading-spinner"></div>
        </div>
        <div id="image-pagination" class="image-pagination">
            <button type="button" id="prev-page" class="btn btn-secondary" disabled="">Previous</button>
            <span id="page-info">Page 1</span>
            <button type="button" id="next-page" class="btn btn-secondary">Next</button>
        </div>
    </div>
</template>


</body></html>