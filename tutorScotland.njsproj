<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">14.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <Name>tutorScotland</Name>
    <RootNamespace>tutorScotland</RootNamespace>
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>78bc1803-971f-43f1-8b00-906d5a04f472</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>
    </StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <ProjectTypeGuids>{3AF33F2E-1136-4D97-BBB7-1795711AC8B8};{349c5851-65df-11da-9384-00065b846f21};{9092AA53-FB77-4645-B42D-1CCCA6BD08BD}</ProjectTypeGuids>
    <NodejsPort>1337</NodejsPort>
    <StartWebBrowser>true</StartWebBrowser>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Content Include=".env">
      <SubType>Code</SubType>
    </Content>
    <Content Include=".gitignore">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\addTutor.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\blog-writer.js" />
    <Content Include="api\blog.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\connection.js" />
    <Content Include="api\connectToDatabase.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\content-manager.js" />
    <Content Include="api\login.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\page.js" />
    <Content Include="api\protected.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\sections.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\upload-image.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="models\Blog.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="models\Order.js" />
    <Content Include="models\Section.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="models\Tutor.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="models\User.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="package-lock.json" />
    <Content Include="public\admin.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\blogWriter.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\cache-buster.js" />
    <Content Include="public\contact.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\css\animation-module.css" />
    <Content Include="public\css\button-module.css" />
    <Content Include="public\css\footer-module.css" />
    <Content Include="public\css\layout-module.css" />
    <Content Include="public\css\nav.css" />
    <Content Include="public\css\responsive-module.css" />
    <Content Include="public\css\typography-module.css" />
    <Content Include="public\debug-visual-editor.js" />
    <Content Include="public\editor.css" />
    <Content Include="public\header-banner.css" />
    <Content Include="public\index.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\images\bannerBackground.PNG" />
    <Content Include="public\images\bannerShield2.png" />
    <Content Include="public\images\bannerWithRibbons.png" />
    <Content Include="public\images\centralShield.png" />
    <Content Include="public\images\childStudy.PNG" />
    <Content Include="public\images\englishTutor.PNG" />
    <Content Include="public\images\englishTutor2.PNG" />
    <Content Include="public\images\englishTutor3.PNG" />
    <Content Include="public\images\favicon2.png" />
    <Content Include="public\images\flag.PNG" />
    <Content Include="public\images\legoLeft.png" />
    <Content Include="public\images\legoRight.png" />
    <Content Include="public\images\mainMaxim.png" />
    <Content Include="public\images\mathsTutor.PNG" />
    <Content Include="public\images\mathsTutor3.PNG" />
    <Content Include="public\images\mathsTutor4.PNG" />
    <Content Include="public\images\parentAndChild.png" />
    <Content Include="public\images\questionCurl.png" />
    <Content Include="public\images\questionPeriod.png" />
    <Content Include="public\images\subscribe-computer.PNG" />
    <Content Include="public\images\tutor0.jpg" />
    <Content Include="public\images\tutor1.jpg" />
    <Content Include="public\images\tutor2.jpg" />
    <Content Include="public\images\weAreTheFuture.PNG" />
    <Content Include="public\js\dynamic-nav.js" />
    <Content Include="public\js\dynamic-page.js" />
    <Content Include="public\js\dynamic-sections-template.html" />
    <Content Include="public\js\dynamic-sections.js" />
    <Content Include="public\js\editor\api-service.js" />
    <Content Include="public\js\editor\editor-state.js" />
    <Content Include="public\js\editor\features\image-browser.js" />
    <Content Include="public\js\editor\features\section-sorter.js" />
    <Content Include="public\js\editor\override-engine.js" />
    <Content Include="public\js\editor\ui-manager.js" />
    <Content Include="public\js\nav-loader.js" />
    <Content Include="public\js\pages.js" />
    <Content Include="public\js\rolling-banner.js" />
    <Content Include="public\js\upload-helper.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\js\video-player.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\js\visual-editor.js" />
    <Content Include="public\page-template.html" />
    <Content Include="public\page.html" />
    <Content Include="public\parents.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\partials\main-nav.html" />
    <Content Include="public\publicConnect.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="index\config.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\about-us.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\login.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\partnerships.html" />
    <Content Include="public\responsive-helper.js" />
    <Content Include="public\style.css">
      <SubType>Code</SubType>
    </Content>
    <Content Include="api\tutors.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\styles2.css">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\test-context-isolation-script.js" />
    <Content Include="public\test-editor.html" />
    <Content Include="public\test-reorder.html" />
    <Content Include="public\test-ve-ids.js" />
    <Content Include="public\tutorConnect.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\tutorDirectory.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\tutorMembership.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="public\tutorszone.html">
      <SubType>Code</SubType>
    </Content>
    <Content Include="seedBlogs.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="seedDatabase.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="package.json" />
    <Content Include="README.md" />
    <Content Include="seedUsers.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="updateDatabase.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="vercel.json">
      <SubType>Code</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="index\" />
    <Folder Include="api\" />
    <Folder Include="models\" />
    <Folder Include="public\" />
    <Folder Include="public\css\" />
    <Folder Include="public\images\" />
    <Folder Include="public\js\" />
    <Folder Include="public\js\editor\" />
    <Folder Include="public\js\editor\features\" />
    <Folder Include="public\partials\" />
  </ItemGroup>
  <Import Project="$(VSToolsPath)\Node.js Tools\Microsoft.NodejsToolsV2.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:48022/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>True</UseCustomServer>
          <CustomServerUrl>http://localhost:1337</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}" User="">
        <WebProjectProperties>
          <StartPageUrl>
          </StartPageUrl>
          <StartAction>CurrentPage</StartAction>
          <AspNetDebugging>True</AspNetDebugging>
          <SilverlightDebugging>False</SilverlightDebugging>
          <NativeDebugging>False</NativeDebugging>
          <SQLDebugging>False</SQLDebugging>
          <ExternalProgram>
          </ExternalProgram>
          <StartExternalURL>
          </StartExternalURL>
          <StartCmdLineArguments>
          </StartCmdLineArguments>
          <StartWorkingDirectory>
          </StartWorkingDirectory>
          <EnableENC>False</EnableENC>
          <AlwaysStartWebServerOnDebug>False</AlwaysStartWebServerOnDebug>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>