/* ===== Visual Editor UI - Loaded only for admins ===== */

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-20px); }
}

/* Enhanced overlays with better visual feedback */
.edit-overlay {
    position: absolute;
    inset: 0;  /* cover parent */
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    z-index: 9990;
    background: rgba(0, 87, 183, 0.08);
    border: 2px dashed transparent;
    border-radius: 4px;
}

/* Dynamic page content overlay */
.page-edit-overlay {
    position: absolute;
    inset: 0;  /* cover parent */
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    z-index: 9990;
    background: rgba(124, 167, 217, 0.08); /* Light blue tint for page content */
    border: 2px dashed transparent;
    border-radius: 1.5rem; /* Match the two-col-content border radius */
}

/* Active state with improved hover effects */
body.ve-edit-active .edit-overlay,
body.ve-edit-active .page-edit-overlay {
    pointer-events: auto;
}

body.ve-edit-active .ve-img-wrap:hover > .edit-overlay,
body.ve-edit-active .edit-overlay:hover {
    opacity: 1;
    border-color: #0057B7;
    background: rgba(0, 87, 183, 0.12);
    box-shadow: 0 2px 8px rgba(0, 87, 183, 0.2);
}

body.ve-edit-active .page-edit-overlay:hover {
    opacity: 1;
    border-color: #7CA7D9;
    background: rgba(124, 167, 217, 0.15);
    box-shadow: 0 2px 8px rgba(124, 167, 217, 0.3);
}

/* Enhanced edit controls positioning */
.edit-controls,
.page-edit-controls {
    position: absolute;
    top: 8px;
    left: 8px;  /* ✅ MOVED: Edit tab now on the left side */
    z-index: 9991;
}

/* Enhanced edit button styling - larger and more prominent */
.edit-btn,
.page-edit-btn {
    background: linear-gradient(90deg, #B8D4FF 0%, #0057B7 50%, #001B44 100%);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 87, 183, 0.3);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.edit-btn:hover {
    background: #b37cb3; /* Lilac color as requested */
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 16px rgba(179, 124, 179, 0.4);
}

/* Modal hidden until JS opens it */
#editor-modal {
    display: none;
}

body.ve-edit-active #editor-modal[style*="display: block"] {
    display: block;
}

/* Edit mode toggle button */
#edit-mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10000;
    padding: 0.5em 1.2em;
    border-radius: 4px;
    background: #007bff;
    color: #fff;
    border: none;
    cursor: pointer;
    transition: background 0.2s;
    font-size: 14px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

#edit-mode-toggle:hover,
#edit-mode-toggle:focus {
    background: #0056b3;
    outline: none;
}

#edit-mode-toggle[aria-pressed="true"] {
    background: #28a745;
}

/* Image wrapper */
.ve-img-wrap {
    position: relative;
    display: inline-block;
}

/* Edit instructions */
#edit-instructions {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    padding: 15px 25px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 10001;
    animation: slideDown 0.3s ease;
}

/* Modal styles - Enhanced for better UX */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    z-index: 10002;
    backdrop-filter: blur(2px);
}

.modal-content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 0;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    z-index: 10003;
    max-width: 95%;
    width: 800px; /* Increased from 600px */
    max-height: 95vh; /* Increased from 90vh */
    overflow: hidden;
    border: 1px solid #e0e0e0;
}

/* Modal header with improved styling */
.modal-header {
    background: linear-gradient(90deg, #B8D4FF 0%, #0057B7 50%, #001B44 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

/* Close button moved to top right with better styling */
.close-btn {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    line-height: 1;
}

.close-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    transform: scale(1.1);
}

/* Modal body with better spacing */
.modal-body {
    padding: 30px;
    max-height: calc(95vh - 140px);
    overflow-y: auto;
}

/* Enhanced form elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: #fafafa;
}

.form-control:focus {
    outline: none;
    border-color: #0057B7;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(0, 87, 183, 0.1);
}

/* Much larger text areas for better editing experience */
#content-text {
    min-height: 150px; /* Increased from default */
    resize: vertical;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
}

#content-html {
    min-height: 200px; /* Increased from default */
    resize: vertical;
    font-family: 'Courier New', monospace;
    line-height: 1.4;
}

/* Enhanced button styling to match site theme */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s ease;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(90deg, #B8D4FF 0%, #0057B7 50%, #001B44 100%);
    color: #fff;
    box-shadow: 0 2px 8px rgba(0,87,183,0.2);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0,87,183,0.3);
    background: linear-gradient(90deg, #A0C4FF 0%, #0048A0 50%, #001530 100%);
}

.btn-secondary {
    background: #6c757d;
    color: #fff;
    box-shadow: 0 2px 8px rgba(108,117,125,0.2);
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(108,117,125,0.3);
}

.btn-warning {
    background: #ffc107;
    color: #000;
    box-shadow: 0 2px 8px rgba(255,193,7,0.2);
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(255,193,7,0.3);
}

.btn-danger {
    background: #dc3545;
    color: #fff;
    box-shadow: 0 2px 8px rgba(220,53,69,0.2);
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(220,53,69,0.3);
}

/* Form actions with better spacing */
.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #f0f0f0;
    flex-wrap: wrap;
}

/* Content type selector styling */
#content-type {
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    color: #666;
    cursor: not-allowed;
}

/* Enhanced image input group */
.image-input-group {
    display: flex;
    gap: 12px;
    align-items: stretch;
    margin-bottom: 15px;
}

.image-input-group input {
    flex: 1;
}

.image-input-group button {
    flex-shrink: 0;
}

/* Image preview styling */
#image-preview {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

#image-preview img {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Upload section styling */
.upload-section {
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

.upload-section label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
}

/* Form check styling for checkboxes */
.form-check {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
}

.form-check-input {
    width: auto;
    margin: 0;
}

.form-check-label {
    margin: 0;
    cursor: pointer;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-height: 90vh;
        margin: 20px;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-body {
        padding: 20px;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        margin-bottom: 8px;
    }

    .image-input-group {
        flex-direction: column;
    }

    .edit-btn {
        font-size: 12px;
        padding: 6px 12px;
    }
}

/* Progress bar styling */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #B8D4FF 0%, #0057B7 50%, #001B44 100%);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.9rem;
    color: #6c757d;
    text-align: center;
    display: block;
}

/* Text Button Management Styles */
.text-button-management {
    margin-top: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.text-button-management h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 1.1rem;
    font-weight: 600;
}

.help-text {
    margin: 0 0 15px 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.text-buttons-list {
    margin-bottom: 15px;
}

.text-button-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 8px;
}

.text-button-info {
    flex: 1;
}

.text-button-info .button-text {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
}

.text-button-info .button-url {
    font-size: 0.85rem;
    color: #6c757d;
    word-break: break-all;
}

.text-button-actions {
    display: flex;
    gap: 8px;
}

.text-button-actions .btn {
    padding: 6px 12px;
    font-size: 0.85rem;
}

.button-form {
    background: white;
    padding: 20px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    margin-bottom: 15px;
}

.button-form-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.button-form-actions .btn {
    padding: 8px 16px;
}

/* Aurora button preview in the management interface */
.text-button-preview {
    display: inline-block;
    background: linear-gradient(90deg, #B8D4FF 0%, #0057B7 50%, #001B44 100%);
    color: #fff;
    border: none;
    border-radius: 2rem;
    padding: 0.6em 1.5em;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    margin-right: 10px;
    pointer-events: none;
}

/* Image browser */
.image-browser {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 10004;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.image-item {
    position: relative;
    aspect-ratio: 1;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 4px;
    overflow: hidden;
}

.image-item:hover {
    border-color: #007bff;
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Visual indicator for images without thumbnails */
.image-item.no-thumbnail {
    border-color: #ffc107;
    background: linear-gradient(45deg, transparent 40%, rgba(255, 193, 7, 0.1) 50%, transparent 60%);
}

.image-item.no-thumbnail:hover {
    border-color: #e0a800;
}

.image-item.no-thumbnail::after {
    content: "⚠";
    position: absolute;
    top: 5px;
    right: 5px;
    background: #ffc107;
    color: #000;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

/* Loading spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.ve-btn {
    display: inline-block;
    padding: 0.5em 1.2em;
    margin: 0.25em;
    border-radius: 4px;
    background: #007bff;
    color: #fff;
    transition: background 0.2s;
}

.ve-btn:hover,
.ve-btn:focus {
    background: #0056b3;
}

/* === Visual-editor fixes for the circular team-member photos ================ */

/* 1) Put the edit toolbox in the dead-centre of the picture */
.team-member .ve-img-wrap .edit-controls {
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;  /* centre it */
}

/* 2) Make sure the toolbox isn't cropped by the img mask */
.team-member .ve-img-wrap {        /* only the extra span we inject */
    overflow: visible !important;  /* keeps the outer circle clipping intact */
}

/* 3) Optional: Tweak the button's appearance for better visibility */
.team-member .ve-img-wrap .edit-btn {
    font-size: 11px;           /* a shade smaller */
    padding: 2px 6px;
    background: #20c997;       /* teal stands out, but pick any */
    opacity: 0.95;             /* subtle transparency */
}

/* ===== Drag Handle Styles for Section Reordering ===== */
.ve-drag-handle {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 123, 255, 0.9);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: grab;
    font-size: 16px;
    font-weight: bold;
    z-index: 1000;
    user-select: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: all 0.2s ease;
}

.ve-drag-handle:hover {
    background: rgba(0, 123, 255, 1);
    transform: scale(1.05);
}

.ve-drag-handle:active {
    cursor: grabbing;
    transform: scale(0.95);
}

/* Section reordering styles */
.ve-reorderable {
    position: relative;
    transition: all 0.3s ease;
}

.ve-reorderable:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.sortable-ghost {
    opacity: 0.5;
    background: #f0f8ff;
    border: 2px dashed #007bff;
}

.sortable-chosen {
    transform: rotate(2deg);
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
}

/* Notification styles */
.ve-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-weight: bold;
    z-index: 10001;
    animation: slideDown 0.3s ease-out;
}

.ve-notification.ve-success { background: #4CAF50; }
.ve-notification.ve-error { background: #f44336; }
.ve-notification.ve-info { background: #2196F3; }

/* ===== Context-Aware Visual Indicators ===== */

/* Context badges in modal titles */
.context-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 8px;
    vertical-align: middle;
}

.context-badge.context-header {
    background: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
}

.context-badge.context-footer {
    background: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #ce93d8;
}

.context-badge.context-nav {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

.context-badge.context-main {
    background: #fff3e0;
    color: #ef6c00;
    border: 1px solid #ffcc02;
}

/* Context-specific modal styling */
.ve-modal-container.context-header .modal-content {
    border-top: 4px solid #1565c0;
}

.ve-modal-container.context-footer .modal-content {
    border-top: 4px solid #7b1fa2;
}

.ve-modal-container.context-nav .modal-content {
    border-top: 4px solid #2e7d32;
}

.ve-modal-container.context-main .modal-content {
    border-top: 4px solid #ef6c00;
}

/* Context-specific overlay colors */
body.ve-edit-active header .edit-overlay:hover {
    background: rgba(21, 101, 192, 0.15);
    border-color: #1565c0;
}

body.ve-edit-active footer .edit-overlay:hover {
    background: rgba(123, 31, 162, 0.15);
    border-color: #7b1fa2;
}

body.ve-edit-active main .edit-overlay:hover {
    background: rgba(239, 108, 0, 0.15);
    border-color: #ef6c00;
}

/* Context labels on overlays */
.edit-overlay::before {
    content: attr(data-context);
    position: absolute;
    top: -20px;
    left: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    z-index: 10000;
}

.edit-overlay:hover::before {
    opacity: 1;
}

/* === Enhanced Dynamic Section Overlays (Phase 1) === */
.dyn-edit-overlay {
    position: absolute;
    inset: 0;
    background: rgba(200,162,200,0.08);
    border: 2px dashed rgba(200,162,200,0.6);
    border-radius: 8px;
    pointer-events: auto;
    z-index: 9980;
    opacity: 0;
    transition: all 0.3s ease;
}

body.ve-edit-active .dyn-edit-overlay {
    opacity: 1;
}

.dyn-edit-overlay:hover {
    background: rgba(200,162,200,0.15);
    border-color: rgba(200,162,200,0.8);
    box-shadow: 0 4px 12px rgba(200,162,200,0.3);
}

.dyn-edit-controls {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.dyn-edit-btn {
    padding: 8px 12px;
    border-radius: 6px;
    border: none;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dyn-edit-btn.primary {
    background: #c8a2c8;
    color: #333;
}

.dyn-edit-btn.primary:hover {
    background: #b891b8;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.dyn-edit-btn.secondary {
    background: #8bc34a;
    color: white;
}

.dyn-edit-btn.secondary:hover {
    background: #7cb342;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Tertiary button styles removed - no longer needed */

/* Context label for dynamic sections */
.dyn-edit-overlay::before {
    content: "DYNAMIC SECTION";
    position: absolute;
    top: -24px;
    left: 0;
    background: rgba(200,162,200,0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    z-index: 10000;
}

.dyn-edit-overlay:hover::before {
    opacity: 1;
}

/* === Responsive Design for Dynamic Section Overlays === */
@media (max-width: 768px) {
    .dyn-edit-controls {
        /* Keep centered positioning on mobile */
        flex-direction: column;
        gap: 4px;
    }

    .dyn-edit-btn {
        padding: 6px 8px;
        font-size: 11px;
    }

    .dyn-edit-overlay::before {
        font-size: 9px;
        padding: 2px 6px;
    }
}

/* === Visual Consistency with Existing Editor === */
/* Ensure dynamic overlays don't interfere with regular edit overlays */
.dyn-edit-overlay + .edit-overlay,
.edit-overlay + .dyn-edit-overlay {
    z-index: 9981; /* Slightly higher to prevent conflicts */
}

/* Smooth transitions when switching between edit modes */
.dyn-edit-overlay,
.edit-overlay {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus states for accessibility */
.dyn-edit-btn:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

/* Loading state for dynamic sections */
.dyn-block.loading .dyn-edit-overlay {
    opacity: 0.5;
    pointer-events: none;
}

.dyn-block.loading .dyn-edit-overlay::after {
    content: "Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
}