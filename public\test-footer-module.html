<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Footer Module Test - Tutors Alliance Scotland</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <link rel="stylesheet" href="/styles2.css">
    <link rel="stylesheet" href="/css/footer-module.css">
    <link rel="stylesheet" href="/css/button-module.css">
    <link rel="stylesheet" href="/css/typography-module.css">
    <link rel="stylesheet" href="/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/responsive-helper.js"></script>
    <script src="/js/nav-loader.js" defer></script>
    <script src="/js/dynamic-nav.js" defer></script>
    <script src="/js/rolling-banner.js" defer></script>
    <style>
        .test-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 8px;
        }
        .test-results {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-pass { background: #d4edda; border-left: 4px solid #28a745; }
        .test-fail { background: #f8d7da; border-left: 4px solid #dc3545; }
    </style>
</head>
<body data-page="test-footer">
    <header>
        <h1>Footer Module Test</h1>
        <div class="header-links">
            <a href="/" class="banner-login-link login-box">Home</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            Testing Footer Module Extraction
        </div>
    </div>

    <main>
        <div class="test-container">
            <h2>Footer Module Test Page</h2>
            <p>This page tests the extracted footer module to ensure it works correctly before removing styles from styles2.css.</p>

            <div class="test-section">
                <h3>Test Instructions</h3>
                <ol>
                    <li><strong>Scroll down</strong> to see if the floating social media footer appears</li>
                    <li><strong>Check the static footer</strong> at the bottom of the page</li>
                    <li><strong>Test responsive behavior</strong> by resizing the window</li>
                    <li><strong>Verify footer grid</strong> (if present on this page)</li>
                    <li><strong>Compare with index.html</strong> to ensure identical appearance</li>
                </ol>
            </div>

            <div class="test-section">
                <h3>Expected Footer Behavior</h3>
                <ul>
                    <li>✅ Floating social media footer should appear at bottom when scrolling</li>
                    <li>✅ Footer should have Facebook and Instagram icons</li>
                    <li>✅ Footer background should match existing design</li>
                    <li>✅ Footer should be responsive on mobile</li>
                    <li>✅ Static footer should appear at page bottom with correct styling</li>
                </ul>
            </div>

            <div class="test-section">
                <h3>Test Content (Scroll to see footer)</h3>
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
                <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
                
                <div style="height: 800px; background: linear-gradient(to bottom, #f0f8ff, #e6f0ff); padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <h4>Scroll Area</h4>
                    <p>Keep scrolling to test footer behavior...</p>
                    <div style="height: 200px;"></div>
                    <p>More content...</p>
                    <div style="height: 200px;"></div>
                    <p>Even more content...</p>
                    <div style="height: 200px;"></div>
                    <p>Footer should appear when you scroll down...</p>
                </div>
            </div>

            <div class="test-results" id="testResults">
                <h3>Test Results</h3>
                <p>Manual testing required. Check the items above and compare with the main site.</p>
            </div>
        </div>

        <!-- Test footer grid -->
        <div class="footer-grid">
            <div>
                <h4>Test Section 1</h4>
                <ul>
                    <li><a href="#">Test Link 1</a></li>
                    <li><a href="#">Test Link 2</a></li>
                    <li><a href="#">Test Link 3</a></li>
                </ul>
            </div>
            <div>
                <h4>Test Section 2</h4>
                <ul>
                    <li><a href="#">Test Link 4</a></li>
                    <li><a href="#">Test Link 5</a></li>
                    <li><a href="#">Test Link 6</a></li>
                </ul>
            </div>
            <div>
                <h4>Test Section 3</h4>
                <ul>
                    <li><a href="#">Test Link 7</a></li>
                    <li><a href="#">Test Link 8</a></li>
                    <li><a href="#">Test Link 9</a></li>
                </ul>
            </div>
        </div>
    </main>

    <!-- Static footer -->
    <div class="static-footer">
        <div class="static-footer-container">
            <div class="static-footer-left">
                <h4>Test Footer</h4>
                <ul>
                    <li><a href="#">Test Footer Link 1</a></li>
                    <li><a href="#">Test Footer Link 2</a></li>
                    <li><a href="#">Test Footer Link 3</a></li>
                </ul>
                <div class="static-footer-copyright">
                    <p>&copy; 2024 Tutors Alliance Scotland - Footer Module Test</p>
                </div>
            </div>
            <div class="static-footer-right">
                <div class="static-footer-credits">
                    <p>Testing modular CSS architecture</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating social media footer -->
    <div class="site-footer" id="socialFooter">
        <div class="footer-icons">
            <a href="https://facebook.com" target="_blank" aria-label="Facebook">📘</a>
            <a href="https://instagram.com" target="_blank" aria-label="Instagram">📷</a>
        </div>
    </div>

    <script>
        // Simple footer visibility script for testing
        window.addEventListener('scroll', function() {
            const footer = document.getElementById('socialFooter');
            const scrolled = window.scrollY > 200;
            
            if (scrolled) {
                footer.classList.add('is-visible');
            } else {
                footer.classList.remove('is-visible');
            }
        });

        // Test results
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Footer module test page loaded');
            console.log('Check footer visibility and styling manually');
        });
    </script>
</body>
</html>
